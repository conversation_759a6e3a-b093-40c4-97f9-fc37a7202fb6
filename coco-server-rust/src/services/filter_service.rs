use std::collections::HashMap;

use serde::{Deserialize, Serialize};
use tracing::{debug, warn};

use crate::error::error::CocoError;

/// 过滤服务
/// 负责处理模型提供商的过滤逻辑，包括字段过滤和文本搜索
pub struct FilterService;

/// 过滤条件结构
/// 定义支持的过滤字段和验证规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FilterConditions {
    /// 是否启用过滤
    pub enabled: Option<bool>,
    /// 是否内置提供商过滤
    pub builtin: Option<bool>,
    /// API类型过滤
    pub api_type: Option<String>,
    /// 文本搜索查询
    pub text_query: Option<String>,
}

/// 过滤验证结果
#[derive(Debug, Clone)]
pub struct FilterValidationResult {
    /// 是否验证通过
    pub is_valid: bool,
    /// 验证错误信息
    pub errors: Vec<String>,
    /// 验证警告信息
    pub warnings: Vec<String>,
}

/// 支持的过滤字段枚举
#[derive(Debug, Clone, PartialEq)]
pub enum FilterField {
    /// 启用状态字段
    Enabled,
    /// 内置提供商字段
    Builtin,
    /// API类型字段
    ApiType,
    /// 文本搜索字段
    TextQuery,
}

impl FilterService {
    /// 创建新的过滤服务实例
    pub fn new() -> Self {
        Self
    }

    /// 验证过滤条件
    ///
    /// # 参数
    /// * `filters` - 过滤条件映射
    ///
    /// # 返回
    /// * `FilterValidationResult` - 验证结果
    pub fn validate_filters(
        &self,
        filters: &HashMap<String, serde_json::Value>,
    ) -> FilterValidationResult {
        let mut errors = Vec::new();
        let mut warnings = Vec::new();

        debug!("开始验证过滤条件: {:?}", filters);

        for (key, value) in filters {
            match self.validate_filter_field(key, value) {
                Ok(warning) => {
                    if let Some(warning_msg) = warning {
                        warnings.push(warning_msg);
                    }
                }
                Err(error_msg) => {
                    errors.push(error_msg);
                }
            }
        }

        let is_valid = errors.is_empty();

        debug!(
            "过滤条件验证完成: valid={}, errors={}, warnings={}",
            is_valid,
            errors.len(),
            warnings.len()
        );

        FilterValidationResult {
            is_valid,
            errors,
            warnings,
        }
    }

    /// 验证单个过滤字段
    ///
    /// # 参数
    /// * `field` - 字段名
    /// * `value` - 字段值
    ///
    /// # 返回
    /// * `Result<Option<String>, String>` -
    ///   成功时返回可选警告，失败时返回错误信息
    fn validate_filter_field(
        &self,
        field: &str,
        value: &serde_json::Value,
    ) -> Result<Option<String>, String> {
        match field {
            "enabled" => {
                if !value.is_boolean() {
                    return Err(format!(
                        "字段 '{}' 必须是布尔值，当前类型: {}",
                        field,
                        self.get_value_type(value)
                    ));
                }
                Ok(None)
            }
            "builtin" => {
                if !value.is_boolean() {
                    return Err(format!(
                        "字段 '{}' 必须是布尔值，当前类型: {}",
                        field,
                        self.get_value_type(value)
                    ));
                }
                Ok(None)
            }
            "api_type" => {
                if !value.is_string() {
                    return Err(format!(
                        "字段 '{}' 必须是字符串，当前类型: {}",
                        field,
                        self.get_value_type(value)
                    ));
                }

                // 验证API类型值是否有效
                if let Some(api_type) = value.as_str() {
                    if api_type.trim().is_empty() {
                        return Err("api_type 不能为空字符串".to_string());
                    }

                    // 检查是否为已知的API类型
                    if !self.is_known_api_type(api_type) {
                        return Ok(Some(format!("未知的API类型: {}", api_type)));
                    }
                }
                Ok(None)
            }
            _ => {
                // 对于未知字段，记录警告但不阻止处理
                warn!("未知的过滤字段: {}", field);
                Ok(Some(format!("未知的过滤字段: {}", field)))
            }
        }
    }

    /// 获取值的类型描述
    fn get_value_type(&self, value: &serde_json::Value) -> &'static str {
        match value {
            serde_json::Value::Null => "null",
            serde_json::Value::Bool(_) => "boolean",
            serde_json::Value::Number(_) => "number",
            serde_json::Value::String(_) => "string",
            serde_json::Value::Array(_) => "array",
            serde_json::Value::Object(_) => "object",
        }
    }

    /// 检查是否为已知的API类型
    ///
    /// # 参数
    /// * `api_type` - API类型字符串
    ///
    /// # 返回
    /// * `bool` - 是否为已知类型
    fn is_known_api_type(&self, api_type: &str) -> bool {
        const KNOWN_API_TYPES: &[&str] = &[
            "openai",
            "anthropic",
            "google",
            "azure",
            "deepseek",
            "moonshot",
            "zhipu",
            "baidu",
            "alibaba",
            "tencent",
            "custom",
        ];

        KNOWN_API_TYPES.contains(&api_type.to_lowercase().as_str())
    }

    /// 解析过滤条件
    ///
    /// # 参数
    /// * `filters` - 过滤条件映射
    ///
    /// # 返回
    /// * `Result<FilterConditions, CocoError>` - 解析结果
    pub fn parse_filter_conditions(
        &self,
        filters: &HashMap<String, serde_json::Value>,
    ) -> Result<FilterConditions, CocoError> {
        // 先验证过滤条件
        let validation_result = self.validate_filters(filters);
        if !validation_result.is_valid {
            return Err(CocoError::ModelProviderValidation(format!(
                "过滤条件验证失败: {}",
                validation_result.errors.join(", ")
            )));
        }

        // 记录警告
        for warning in &validation_result.warnings {
            warn!("过滤条件警告: {}", warning);
        }

        // 解析各个字段
        let enabled = filters.get("enabled").and_then(|v| v.as_bool());
        let builtin = filters.get("builtin").and_then(|v| v.as_bool());
        let api_type = filters
            .get("api_type")
            .and_then(|v| v.as_str())
            .map(|s| s.to_string());
        let text_query = filters
            .get("q")
            .and_then(|v| v.as_str())
            .map(|s| s.to_string());

        Ok(FilterConditions {
            enabled,
            builtin,
            api_type,
            text_query,
        })
    }

    /// 验证文本搜索查询
    ///
    /// # 参数
    /// * `query` - 搜索查询字符串
    ///
    /// # 返回
    /// * `Result<(), CocoError>` - 验证结果
    pub fn validate_text_query(&self, query: &str) -> Result<(), CocoError> {
        if query.trim().is_empty() {
            return Err(CocoError::ModelProviderValidation(
                "搜索查询不能为空".to_string(),
            ));
        }

        // 检查查询长度
        if query.len() > 1000 {
            return Err(CocoError::ModelProviderValidation(
                "搜索查询长度不能超过1000个字符".to_string(),
            ));
        }

        // 检查是否包含危险字符（防止注入攻击）
        if self.contains_dangerous_characters(query) {
            return Err(CocoError::ModelProviderValidation(
                "搜索查询包含不安全的字符".to_string(),
            ));
        }

        Ok(())
    }

    /// 检查是否包含危险字符
    fn contains_dangerous_characters(&self, query: &str) -> bool {
        // 检查SQL注入相关的危险字符
        const DANGEROUS_PATTERNS: &[&str] = &[
            "';", "--", "/*", "*/", "xp_", "sp_", "DROP", "DELETE", "INSERT", "UPDATE", "EXEC",
        ];

        let query_upper = query.to_uppercase();
        DANGEROUS_PATTERNS
            .iter()
            .any(|pattern| query_upper.contains(pattern))
    }

    /// 获取支持的过滤字段列表
    ///
    /// # 返回
    /// * `Vec<FilterField>` - 支持的字段列表
    pub fn get_supported_fields(&self) -> Vec<FilterField> {
        vec![
            FilterField::Enabled,
            FilterField::Builtin,
            FilterField::ApiType,
            FilterField::TextQuery,
        ]
    }

    /// 获取字段的描述信息
    ///
    /// # 参数
    /// * `field` - 过滤字段
    ///
    /// # 返回
    /// * `&'static str` - 字段描述
    pub fn get_field_description(&self, field: &FilterField) -> &'static str {
        match field {
            FilterField::Enabled => "过滤启用状态，布尔值",
            FilterField::Builtin => "过滤内置提供商，布尔值",
            FilterField::ApiType => "过滤API类型，字符串值",
            FilterField::TextQuery => "文本搜索，在name、description字段中搜索",
        }
    }
}

impl Default for FilterService {
    fn default() -> Self {
        Self::new()
    }
}

impl Default for FilterConditions {
    fn default() -> Self {
        Self {
            enabled: None,
            builtin: None,
            api_type: None,
            text_query: None,
        }
    }
}

impl FilterConditions {
    /// 创建新的过滤条件
    pub fn new() -> Self {
        Self::default()
    }

    /// 设置启用状态过滤
    pub fn with_enabled(mut self, enabled: bool) -> Self {
        self.enabled = Some(enabled);
        self
    }

    /// 设置内置提供商过滤
    pub fn with_builtin(mut self, builtin: bool) -> Self {
        self.builtin = Some(builtin);
        self
    }

    /// 设置API类型过滤
    pub fn with_api_type(mut self, api_type: String) -> Self {
        self.api_type = Some(api_type);
        self
    }

    /// 设置文本搜索
    pub fn with_text_query(mut self, text_query: String) -> Self {
        self.text_query = Some(text_query);
        self
    }

    /// 检查是否有任何过滤条件
    pub fn has_any_filter(&self) -> bool {
        self.enabled.is_some()
            || self.builtin.is_some()
            || self.api_type.is_some()
            || self.text_query.is_some()
    }

    /// 检查是否有文本搜索
    pub fn has_text_search(&self) -> bool {
        self.text_query
            .as_ref()
            .map_or(false, |q| !q.trim().is_empty())
    }
}

#[cfg(test)]
mod tests {
    use serde_json::json;

    use super::*;

    #[test]
    fn test_filter_service_creation() {
        let service = FilterService::new();
        let supported_fields = service.get_supported_fields();
        assert_eq!(supported_fields.len(), 4);
        assert!(supported_fields.contains(&FilterField::Enabled));
        assert!(supported_fields.contains(&FilterField::Builtin));
        assert!(supported_fields.contains(&FilterField::ApiType));
        assert!(supported_fields.contains(&FilterField::TextQuery));
    }

    #[test]
    fn test_validate_filters_success() {
        let service = FilterService::new();
        let mut filters = HashMap::new();
        filters.insert("enabled".to_string(), json!(true));
        filters.insert("builtin".to_string(), json!(false));
        filters.insert("api_type".to_string(), json!("openai"));

        let result = service.validate_filters(&filters);
        assert!(result.is_valid);
        assert!(result.errors.is_empty());
    }

    #[test]
    fn test_validate_filters_invalid_types() {
        let service = FilterService::new();
        let mut filters = HashMap::new();
        filters.insert("enabled".to_string(), json!("not_boolean"));
        filters.insert("builtin".to_string(), json!(123));
        filters.insert("api_type".to_string(), json!(true));

        let result = service.validate_filters(&filters);
        assert!(!result.is_valid);
        assert_eq!(result.errors.len(), 3);
    }

    #[test]
    fn test_validate_filters_unknown_field() {
        let service = FilterService::new();
        let mut filters = HashMap::new();
        filters.insert("unknown_field".to_string(), json!("value"));

        let result = service.validate_filters(&filters);
        assert!(result.is_valid); // 未知字段不阻止验证通过
        assert_eq!(result.warnings.len(), 1);
        assert!(result.warnings[0].contains("未知的过滤字段"));
    }

    #[test]
    fn test_validate_api_type() {
        let service = FilterService::new();

        // 测试已知API类型
        assert!(service.is_known_api_type("openai"));
        assert!(service.is_known_api_type("anthropic"));
        assert!(service.is_known_api_type("deepseek"));

        // 测试未知API类型
        assert!(!service.is_known_api_type("unknown_api"));

        // 测试大小写不敏感
        assert!(service.is_known_api_type("OpenAI"));
        assert!(service.is_known_api_type("ANTHROPIC"));
    }

    #[test]
    fn test_parse_filter_conditions() {
        let service = FilterService::new();
        let mut filters = HashMap::new();
        filters.insert("enabled".to_string(), json!(true));
        filters.insert("builtin".to_string(), json!(false));
        filters.insert("api_type".to_string(), json!("openai"));
        filters.insert("q".to_string(), json!("search query"));

        let result = service.parse_filter_conditions(&filters).unwrap();
        assert_eq!(result.enabled, Some(true));
        assert_eq!(result.builtin, Some(false));
        assert_eq!(result.api_type, Some("openai".to_string()));
        assert_eq!(result.text_query, Some("search query".to_string()));
    }

    #[test]
    fn test_validate_text_query() {
        let service = FilterService::new();

        // 测试有效查询
        assert!(service.validate_text_query("valid query").is_ok());

        // 测试空查询
        assert!(service.validate_text_query("").is_err());
        assert!(service.validate_text_query("   ").is_err());

        // 测试过长查询
        let long_query = "a".repeat(1001);
        assert!(service.validate_text_query(&long_query).is_err());

        // 测试危险字符
        assert!(service
            .validate_text_query("'; DROP TABLE users; --")
            .is_err());
        assert!(service.validate_text_query("/* comment */ DELETE").is_err());
    }

    #[test]
    fn test_filter_conditions_builder() {
        let conditions = FilterConditions::new()
            .with_enabled(true)
            .with_builtin(false)
            .with_api_type("openai".to_string())
            .with_text_query("search".to_string());

        assert_eq!(conditions.enabled, Some(true));
        assert_eq!(conditions.builtin, Some(false));
        assert_eq!(conditions.api_type, Some("openai".to_string()));
        assert_eq!(conditions.text_query, Some("search".to_string()));
        assert!(conditions.has_any_filter());
        assert!(conditions.has_text_search());
    }

    #[test]
    fn test_filter_conditions_empty() {
        let conditions = FilterConditions::new();
        assert!(!conditions.has_any_filter());
        assert!(!conditions.has_text_search());
    }

    #[test]
    fn test_field_descriptions() {
        let service = FilterService::new();

        assert!(!service
            .get_field_description(&FilterField::Enabled)
            .is_empty());
        assert!(!service
            .get_field_description(&FilterField::Builtin)
            .is_empty());
        assert!(!service
            .get_field_description(&FilterField::ApiType)
            .is_empty());
        assert!(!service
            .get_field_description(&FilterField::TextQuery)
            .is_empty());
    }

    #[test]
    fn test_dangerous_characters_detection() {
        let service = FilterService::new();

        // 测试安全查询
        assert!(!service.contains_dangerous_characters("normal search query"));
        assert!(!service.contains_dangerous_characters("openai api"));

        // 测试危险查询
        assert!(service.contains_dangerous_characters("'; DROP TABLE"));
        assert!(service.contains_dangerous_characters("-- comment"));
        assert!(service.contains_dangerous_characters("/* comment */"));
        assert!(service.contains_dangerous_characters("EXEC sp_"));
        assert!(service.contains_dangerous_characters("delete from"));
    }
}
