/// TASK-014: 基础过滤功能 - 使用示例
/// 
/// 此示例展示了如何使用 FilterService 进行模型提供商的过滤操作
/// 
/// 运行示例: cargo run --example filter_service_usage

use std::collections::HashMap;
use serde_json::json;

// 引入过滤服务相关类型
use coco_server::services::filter_service::{
    FilterService, FilterConditions, FilterField
};

fn main() {
    println!("=== TASK-014: 基础过滤功能使用示例 ===\n");

    // 1. 创建过滤服务实例
    let filter_service = FilterService::new();
    println!("✅ 创建过滤服务实例成功");

    // 2. 展示支持的过滤字段
    println!("\n📋 支持的过滤字段:");
    let supported_fields = filter_service.get_supported_fields();
    for field in &supported_fields {
        let description = filter_service.get_field_description(field);
        println!("  - {:?}: {}", field, description);
    }

    // 3. 验证有效的过滤条件
    println!("\n✅ 验证有效的过滤条件:");
    let mut valid_filters = HashMap::new();
    valid_filters.insert("enabled".to_string(), json!(true));
    valid_filters.insert("builtin".to_string(), json!(false));
    valid_filters.insert("api_type".to_string(), json!("openai"));

    let validation_result = filter_service.validate_filters(&valid_filters);
    println!("  验证结果: {}", if validation_result.is_valid { "通过" } else { "失败" });
    if !validation_result.warnings.is_empty() {
        println!("  警告: {:?}", validation_result.warnings);
    }

    // 4. 解析过滤条件
    println!("\n🔍 解析过滤条件:");
    match filter_service.parse_filter_conditions(&valid_filters) {
        Ok(conditions) => {
            println!("  解析成功:");
            println!("    enabled: {:?}", conditions.enabled);
            println!("    builtin: {:?}", conditions.builtin);
            println!("    api_type: {:?}", conditions.api_type);
            println!("    text_query: {:?}", conditions.text_query);
        }
        Err(e) => {
            println!("  解析失败: {}", e);
        }
    }

    // 5. 测试无效的过滤条件
    println!("\n❌ 测试无效的过滤条件:");
    let mut invalid_filters = HashMap::new();
    invalid_filters.insert("enabled".to_string(), json!("not_boolean"));
    invalid_filters.insert("api_type".to_string(), json!(123));

    let validation_result = filter_service.validate_filters(&invalid_filters);
    println!("  验证结果: {}", if validation_result.is_valid { "通过" } else { "失败" });
    if !validation_result.errors.is_empty() {
        println!("  错误:");
        for error in &validation_result.errors {
            println!("    - {}", error);
        }
    }

    // 6. 测试文本搜索验证
    println!("\n🔍 测试文本搜索验证:");
    
    // 有效的搜索查询
    let valid_query = "openai gpt";
    match filter_service.validate_text_query(valid_query) {
        Ok(_) => println!("  '{}' - 验证通过", valid_query),
        Err(e) => println!("  '{}' - 验证失败: {}", valid_query, e),
    }

    // 无效的搜索查询（包含危险字符）
    let dangerous_query = "'; DROP TABLE users; --";
    match filter_service.validate_text_query(dangerous_query) {
        Ok(_) => println!("  '{}' - 验证通过", dangerous_query),
        Err(e) => println!("  '{}' - 验证失败: {}", dangerous_query, e),
    }

    // 空查询
    let empty_query = "   ";
    match filter_service.validate_text_query(empty_query) {
        Ok(_) => println!("  '{}' - 验证通过", empty_query),
        Err(e) => println!("  '{}' - 验证失败: {}", empty_query, e),
    }

    // 7. 使用构建器模式创建过滤条件
    println!("\n🏗️ 使用构建器模式创建过滤条件:");
    let conditions = FilterConditions::new()
        .with_enabled(true)
        .with_api_type("anthropic".to_string())
        .with_text_query("claude".to_string());

    println!("  构建的条件:");
    println!("    enabled: {:?}", conditions.enabled);
    println!("    api_type: {:?}", conditions.api_type);
    println!("    text_query: {:?}", conditions.text_query);
    println!("    has_any_filter: {}", conditions.has_any_filter());
    println!("    has_text_search: {}", conditions.has_text_search());

    // 8. 测试API类型验证
    println!("\n🔧 测试API类型验证:");
    let api_types = vec!["openai", "anthropic", "unknown_api", "OPENAI"];
    for api_type in api_types {
        let mut test_filters = HashMap::new();
        test_filters.insert("api_type".to_string(), json!(api_type));
        
        let result = filter_service.validate_filters(&test_filters);
        let status = if result.is_valid {
            if result.warnings.is_empty() { "✅ 有效" } else { "⚠️ 有效(有警告)" }
        } else {
            "❌ 无效"
        };
        println!("  '{}': {}", api_type, status);
    }

    // 9. 展示完整的搜索过滤示例
    println!("\n🎯 完整的搜索过滤示例:");
    let mut search_filters = HashMap::new();
    search_filters.insert("enabled".to_string(), json!(true));
    search_filters.insert("builtin".to_string(), json!(false));
    search_filters.insert("api_type".to_string(), json!("openai"));
    search_filters.insert("q".to_string(), json!("gpt-4"));

    println!("  搜索条件: {:?}", search_filters);
    
    match filter_service.parse_filter_conditions(&search_filters) {
        Ok(conditions) => {
            println!("  解析结果:");
            println!("    - 只显示启用的提供商: {}", conditions.enabled.unwrap_or(false));
            println!("    - 排除内置提供商: {}", !conditions.builtin.unwrap_or(true));
            println!("    - API类型: {}", conditions.api_type.as_deref().unwrap_or("任意"));
            println!("    - 搜索关键词: {}", conditions.text_query.as_deref().unwrap_or("无"));
            
            if conditions.has_text_search() {
                println!("    ✅ 包含文本搜索");
            }
        }
        Err(e) => {
            println!("  ❌ 解析失败: {}", e);
        }
    }

    println!("\n=== 示例完成 ===");
    println!("💡 提示: 这些过滤功能已集成到模型提供商搜索API中");
    println!("   可以通过 GET /model_provider/_search?enabled=true&api_type=openai&q=gpt 使用");
}
